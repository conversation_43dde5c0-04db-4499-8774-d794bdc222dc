import { <PERSON><PERSON>, BackHandler, Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React, { useEffect, useState } from 'react'
import { DrawerContentScrollView, DrawerItem, DrawerItemList } from '@react-navigation/drawer'
import FontAwesome5 from 'react-native-vector-icons/dist/FontAwesome5';
import FontAwesome6 from 'react-native-vector-icons/dist/FontAwesome6';
import Ionicons from 'react-native-vector-icons/dist/Ionicons';
import { CommonActions, useNavigation } from '@react-navigation/native'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { useStateContext } from './StateProvider'
import { openDatabase } from 'react-native-sqlite-storage';


var db = openDatabase({ name: 'UserDatabase.db' });

const CustomDrawerContent = (props) => {

  const { routeNo, setRouteNo } = useStateContext();
  const navigation = useNavigation();

  useEffect(() => {
    const getProducts = async () => {
      const storProducts = await AsyncStorage.getItem('routeNo');
      setRouteNo(JSON.parse(storProducts))
    };

    getProducts();
  }, []);


 
  const logout = async () => {
    try {
      // ✅ Delete the Customers table from SQLite
      db.transaction((tx) => {
        tx.executeSql(
          "DROP TABLE IF EXISTS Customers",
          [],
          () => {
          },
          (_, error) => {
            Alert.alert('❌ Error deleting Customers table:', error)
          }
        );
      });
      await AsyncStorage.clear();
      BackHandler.exitApp();
    } catch (error) {
      Alert.alert('Error', `Failed to logout: ${error.message}`);
    }
  };
  

  return (
    <DrawerContentScrollView {...props} contentContainerStyle={styles.drawerContent}>
      <View style={styles.container}>
        {/* 🔹 User Profile Section */}
        <View style={styles.profileContainer}>
          <Image style={styles.profileImage} source={require('./images/milk.png')} />
          <Text style={styles.profileText}>{routeNo}</Text>
        </View>

        {/* 🔹 Drawer Items TodayTransactions MacAddress */}
        <View style={styles.drawerItems}>
          <DrawerItem icon={() => <FontAwesome5 name="home" color="#93c5fd" size={24} />} label="Home" labelStyle={styles.drawerLabel} onPress={() => navigation.navigate('Home')} />
          <DrawerItem icon={() => <FontAwesome6 name="money-bill-transfer" color="#fda8f3" size={24} />} label="All Transaction" labelStyle={styles.drawerLabel} onPress={() => navigation.navigate('Transaction')} />
          <DrawerItem icon={() => <Ionicons name="today-outline" color="#525252" size={24} />} label="Today Transaction" labelStyle={styles.drawerLabel} onPress={() => navigation.navigate('TodayTransaction')} />
          <DrawerItem icon={() => <FontAwesome6 name="people-group" color="gray" size={24} />} label="CustomersList" labelStyle={styles.drawerLabel} onPress={() => navigation.navigate('CustomersList')} />
        </View>

        {/* 🔹 Logout Button at the Bottom */}
        <View style={styles.logoutContainer}>
          <TouchableOpacity onPress={logout} style={styles.logoutButton}>
            <Text style={styles.logoutText}>Logout</Text>
          </TouchableOpacity>
        </View>
      </View>
    </DrawerContentScrollView>
  )
}

export default CustomDrawerContent
const styles = StyleSheet.create({
  drawerContent: {
    flexGrow: 1, // Makes sure content stretches fully
  },
  container: {
    flex: 1, // Makes the whole container take full height
    justifyContent: 'space-between', // Push logout button to the bottom
  },
  profileContainer: {
    alignItems: 'center',
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'gray',
  },
  profileImage: {
    width: 100,
    height: 100,
    resizeMode: 'contain',
  },
  profileText: {
    textAlign: 'center',
    fontWeight: 'bold',
    fontSize: 22,
    paddingVertical: 10,
  },
  drawerItems: {
    flex: 1, // Allows items to take space and push logout down
  },
  drawerLabel: {
    color: 'gray',
  },
  logoutContainer: {
    padding: 15,
  },
  logoutButton: {
    backgroundColor: 'red',
    padding: 15,
    borderRadius: 10,
  },
  logoutText: {
    textAlign: 'center',
    color: 'white',
    fontWeight: 'bold',
  },
});


