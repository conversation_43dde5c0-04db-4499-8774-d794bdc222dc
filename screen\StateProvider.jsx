

import AsyncStorage from "@react-native-async-storage/async-storage";
import { createContext, useContext, useEffect, useState } from "react";
import BackgroundFetch from "react-native-background-fetch";
import NetInfo from '@react-native-community/netinfo';
import { openDatabase } from 'react-native-sqlite-storage';
import Config from "react-native-config";
import { Alert } from "react-native";

const db = openDatabase({ name: 'UserDatabase.db' });

const StateContext = createContext();
const apiBaseUrl = Config.API_BASE_URL;

// ✅ Declare this outside to make it exportable
let syncTransactions = async () => { };

// ✅ Extracted to top-level so we can export it!
const refreshCustomers = async () => {
  // console.log("⏳ Running background refresh...");

  const netInfo = await NetInfo.fetch();
  if (!netInfo.isConnected) {
    // console.log("⚠️ No internet connection. Skipping refresh.");
    return;
  }

  const unsyncedTransactions = await new Promise((resolve, reject) => {
    db.transaction((tx) => {
      tx.executeSql(
        "SELECT * FROM Transactions WHERE sync = 0",
        [],
        (_, { rows }) => {
          const temp = [];
          for (let i = 0; i < rows.length; i++) temp.push(rows.item(i));
          resolve(temp);
        },
        (_, error) => reject(error)
      );
    });
  });

  if (unsyncedTransactions.length > 0) {
    // console.log(`⚠️ Found ${unsyncedTransactions.length} unsynced transactions. Syncing...`);
    const syncSuccess = await syncTransactions(unsyncedTransactions);
    if (!syncSuccess) {
      // console.log("❌ Failed to sync transactions. Skipping customer refresh.");
      return;
    }
  }

  const storedCardId = await AsyncStorage.getItem('userCardId');
  if (!storedCardId) {
    // console.log("⚠️ No card ID found. Refresh aborted.");
    return;
  }
  const B_TOKEN = await AsyncStorage.getItem('token');
  let token = JSON.parse(B_TOKEN);


  const response = await fetch(`${apiBaseUrl}/customers`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': `Bearer ${token}`
    },

  });

  const result = await response.json();
  if (result.success) {
    await new Promise((resolve, reject) => {
      db.transaction((tx) => {
        result.customers.forEach((customer) => {
          tx.executeSql(
            `INSERT OR REPLACE INTO Customers (id, name, card_id, serial_no, balance) 
             VALUES (?, ?, ?, ?, ?)`,
            [
              customer.id,
              customer.name,
              customer.card_id,
              customer.serial_no,
              parseFloat(customer.balance),
            ],
            // () => console.log(`✅ Customer ${customer.name} updated`),
            (_, error) => reject(error)
          );
        });
        resolve();
      });
    });
    // console.log("✅ Customers refreshed successfully.");
  } else {
    console.log("❌ Failed to refresh customers.");
  }
};

export const StateProvider = ({ children }) => {
  const [user, setUser] = useState('');
  const [routeNo, setRouteNo] = useState('');
  const [auth, setAuth] = useState(false);
  const [transactionLoad, setTransactionLoad] = useState(false);
  const [transactionAndCustomers, setTransactionAndCustomers] = useState(false);

  // ✅ Now we can assign syncTransactions properly from here
  syncTransactions = async (unsyncedTransactions) => {
    console.log('SyncTransactions Called');
    const B_TOKEN = await AsyncStorage.getItem('token');
    let token = JSON.parse(B_TOKEN);
    try {
      const batchSize = 10;
      for (let i = 0; i < unsyncedTransactions.length; i += batchSize) {
        const batch = unsyncedTransactions.slice(i, i + batchSize);

        const response = await fetch(`${apiBaseUrl}/transactions/sync`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({ transactions: batch }),
        });

        const result = await response.json();
        console.log('Sync API Response:', result);

        if (result.success) {
          await new Promise((resolve, reject) => {
            db.transaction((tx) => {
              batch.forEach((transaction) => {
                tx.executeSql(
                  "UPDATE Transactions SET sync = 1 WHERE id = ?",
                  [transaction.id],
                  // () => console.log(`Transaction ID=${transaction.id} synced ✅`),
                  (_, error) => reject(error)
                );
              });
              resolve();
            });
          });
          setTransactionLoad(true);
        } else {
          console.log("❌ Sync failed for batch.");
          return false;
        }
      }
      return true;
    } catch (error) {
      console.error("❌ Sync Transactions Error:", error);
      return false;
    }
  };

  // ⏱ Setup background fetch
  const setupBackgroundFetch = async () => {
    console.log('🚀 Setting up Background Fetch...');
    try {
      const status = await BackgroundFetch.configure(
        {
          minimumFetchInterval: 5,
          stopOnTerminate: false,
          startOnBoot: true,
          enableHeadless: true,
          forceAlarmManager: true,
        },
        async (taskId) => {
          console.log("📡 [BackgroundFetch] Task triggered:", taskId);
          await refreshCustomers();
          BackgroundFetch.finish(taskId);
        },
        (error) => {
          console.error("❌ BackgroundFetch failed to start:", error);
        }
      );
      console.log("✅ BackgroundFetch configured:", status);
    } catch (error) {
      console.error("❌ BackgroundFetch setup error:", error);
    }
  };

  useEffect(() => {
    setupBackgroundFetch();
    return () => {
      BackgroundFetch.stop();
      console.log("🛑 BackgroundFetch stopped.");
    };
  }, []);

  return (
    <StateContext.Provider
      value={{
        user,
        setUser,
        auth,
        setAuth,
        routeNo,
        setRouteNo,
        transactionLoad,
        setTransactionLoad,
        transactionAndCustomers,
        setTransactionAndCustomers,
      }}
    >
      {children}
    </StateContext.Provider>
  );
};

export const useStateContext = () => useContext(StateContext);
export { refreshCustomers }; // ✅ Now valid!
