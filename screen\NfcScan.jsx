// let response = await fetch(`https://portal.dfbms.com/api/operator/verify`, {
// let response = await fetch(`https://uat.dfbms.com/api/transactions/sync`, {

import React, { useEffect, useState, useRef } from 'react';
import {
    View,
    StyleSheet,
    Alert,
    Animated,
    Text,
    TouchableOpacity,
    Modal,
    Image,
    ActivityIndicator,
    Platform,
    PermissionsAndroid,
    BackHandler,
} from 'react-native';
import NfcManager, { NfcTech } from 'react-native-nfc-manager';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { DrawerActions, useNavigation } from '@react-navigation/native';
import { useStateContext } from './StateProvider';
// import { Image } from 'react-native-reanimated/lib/typescript/Animated';
import { openDatabase } from 'react-native-sqlite-storage';
import DeviceInfo from 'react-native-device-info';
import LoadingComponent from './LoadingComponent';
import Config from 'react-native-config';



var db = openDatabase({ name: 'UserDatabase.db' });

// Pre-step: Initialize NFC Manager
NfcManager.start();


const NfcScan = () => {
    const [modalVisible, setModalVisible] = useState(false);
    const [loading, setLoading] = useState(false);
    const { auth, setAuth, setRouteNo } = useStateContext();
     const apiBaseUrl = Config.API_BASE_URL;
    

    const [position] = useState(new Animated.Value(0));
    const navigation = useNavigation();


    useEffect(() => {


        initializeDatabase();
        startAnimation();
        // NfcManager.cancelTechnologyRequest();

        // Add NFC availability check
        checkNfcAvailability();
        NfcManager.cancelTechnologyRequest();

    }, []);

    const checkNfcAvailability = async () => {
        try {
            const isSupported = await NfcManager.isSupported();
            if (!isSupported) {
                Alert.alert('NFC Not Supported', 'This device does not support NFC.',
                    [
                        { text: 'OK', onPress: () => BackHandler.exitApp() },
                    ]
                );
                return;
            }

            const isEnabled = await NfcManager.isEnabled();
            if (!isEnabled) {
                Alert.alert(
                    'NFC Not Enabled',
                    'Please enable NFC in your device settings to use this feature.',
                    [
                        {
                            text: 'Cancel',
                            style: 'cancel',
                            onPress: () => BackHandler.exitApp()
                        },
                        { text: 'Open Settings', onPress: () => NfcManager.goToNfcSetting() }
                    ]
                );
            }
        } catch (error) {
            Alert.alert('NFC Check Error:', error)
        }
    };


    const startAnimation = () => {
        Animated.loop(
            Animated.sequence([
                Animated.timing(position, {
                    toValue: 10,
                    duration: 500,
                    useNativeDriver: true,
                }),
                Animated.timing(position, {
                    toValue: -10,
                    duration: 500,
                    useNativeDriver: true,
                }),
            ])
        ).start();
    };

    const initializeDatabase = () => {
        db.transaction((txn) => {
            // ✅ Create Transactions Table if it doesn't exist
            txn.executeSql(
                `CREATE TABLE IF NOT EXISTS Transactions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT, 
                    operator_id INTEGER, 
                    route_id INTEGER, 
                    customer_id INTEGER, 
                    serial_no TEXT, 
                    name TEXT, 
                    product_id INTEGER, 
                    quantity INTEGER, 
                    status TEXT, 
                    created_at TEXT, 
                    total_price REAL, 
                    sync INTEGER
                )`,
                [],
                () => console.log("✅ Transactions Table Created"),
                (_, error) => console.log("❌ Error creating Transactions table:", error)
            );

            // ✅ Create Customers Table if it doesn't exist
            txn.executeSql(
                `CREATE TABLE IF NOT EXISTS Customers (
                    id INTEGER PRIMARY KEY,
                    name TEXT,
                    card_id TEXT UNIQUE,
                    serial_no TEXT,
                    balance REAL
                    
                )`,
                [],
                () => console.log("✅ Customers Table Created"),
                (_, error) => console.log("❌ Error creating Customers table:", error)
            );
        });
    };


    const insertCustomers = (customers) => {
        db.transaction((txn) => {
            customers.forEach((customer) => {
                txn.executeSql(
                    `INSERT OR REPLACE INTO Customers (id, name, card_id, serial_no, balance) 
                 VALUES (?, ?, ?, ?, ?)`,
                    [
                        customer.id,
                        customer.name,
                        customer.card_id,
                        customer.serial_no,
                        parseFloat(customer.balance),
                    ],
                    () => console.log(`✅ Customer ${customer.name} inserted/updated`),
                    (_, error) => Alert.alert("❌ Error inserting customer:", error)
                        //  console.log("❌ Error inserting customer:", error)
                );
            });
        });
    };

    // get a macAddress
    // ✅ Make this return mac instead of setting state only
    const requestPermissionAndFetchMac = async () => {
        try {
            if (Platform.OS === 'android' && Platform.Version >= 23) {
                const granted = await PermissionsAndroid.request(
                    PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
                    {
                        title: 'Location Permission Required',
                        message: 'App needs location permission to get MAC Address',
                        buttonNeutral: 'Ask Me Later',
                        buttonNegative: 'Cancel',
                        buttonPositive: 'OK',
                    }
                );

                if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
                    Alert.alert('Permission Denied', 'Location permission is required to get MAC address.');
                    return null;
                }
            }

            const mac = await DeviceInfo.getMacAddress();
            return mac;

        } catch (err) {
            Alert.alert('❌ MAC fetch error:', err)
            return null;
        }
    };




    const startNfcScan = async () => {
        const isEnabled = await NfcManager.isEnabled();
        if (!isEnabled) {
            return checkNfcAvailability();
        }
        // await checkNfcAvailability();
        setModalVisible(true);

        try {
            await NfcManager.requestTechnology(NfcTech.Ndef);
            setLoading(true)
            const tag = await NfcManager.getTag();

            const cardId = tag?.id || tag?.techList?.[0];
            // console.log('cardiiiiddd', cardId)
            const mac = await requestPermissionAndFetchMac();
            if (!mac) {
                Alert.alert("MAC Address Error", "Could not fetch MAC address.");
                setModalVisible(false);
                return;
            }


            try {
                let response = await fetch(`${apiBaseUrl}/operator/verify`, {
                    method: 'POST',
                    headers: {
                        'accept': 'application/json',
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        card_id: cardId,
                        mac: mac,
                    }),
                });

                const result = await response.json();
                if (result.success) {
                    let B_Token= result.token;
                    await AsyncStorage.setItem('token', JSON.stringify(B_Token));
                    setRouteNo(JSON.stringify(result.route_name))
                    await AsyncStorage.setItem('userCardId', JSON.stringify(cardId));
                    await AsyncStorage.setItem('routeNo', JSON.stringify(result.route_name));
                    await AsyncStorage.setItem('operator_id', JSON.stringify(result.operator_id));


                    //New api add////////////////////
                    try {
                        let customer_res = await fetch(`${apiBaseUrl}/customers`, {
                            method: 'GET',
                            headers: {
                                'Accept': 'application/json',
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${B_Token}`, // ✅ Correct header key and format
                            },
                        });
                        const data = await customer_res.json();
                        if (data.success) {
                            await AsyncStorage.setItem('customers', JSON.stringify(data.customers));
                            await AsyncStorage.setItem('products', JSON.stringify(data.products));

                            insertCustomers(data.customers);

                            const checkFirstLaunch = async () => {
                                const isFirstLaunch = await AsyncStorage.getItem('isFirstLaunch');
                                if (!isFirstLaunch) {

                                    await AsyncStorage.setItem('isFirstLaunch', 'true');
                                    setAuth(true);
                                }
                            };
                            setModalVisible(false);
                            NfcManager.cancelTechnologyRequest();
                            checkFirstLaunch();

                        }
                        else {
                            Alert.alert('Error', 'second api error  .');
                            setModalVisible(false);
                            NfcManager.cancelTechnologyRequest();

                        }
                    } catch (error) {
                        Alert.alert('Error', error.message || error)
                        setModalVisible(false);
                        NfcManager.cancelTechnologyRequest();

                    }

                } else {
                    // console.log("resulte Success  false",);
                    Alert.alert('Error', 'Card not recognized. Please scan again.');
                    setModalVisible(false);
                    NfcManager.cancelTechnologyRequest();

                }
            } catch (error) {
                Alert.alert('Error', error?.message || 'Network request failed. Please try again.');
                setModalVisible(false);
                NfcManager.cancelTechnologyRequest();
            }


        } catch (error) {
            Alert.alert('NFC Error', error?.message || error?.toString?.() || 'Unknown NFC error');
            

        } finally {
            await NfcManager.cancelTechnologyRequest();
            // setIsScanning(false);
            setLoading(false)
        }
    };







    const terminateScan = () => {
        Alert.alert('Scan Cancelled', 'You have cancelled the scanning process.');
        NfcManager.cancelTechnologyRequest();
        setModalVisible(false);
    };


    if (loading) {
        return (
            <LoadingComponent/>
        );
    }

    return (
        <View style={styles.wrapper}>

            <View style={{ flex: 2, }}>
                <View style={{ width: '70%', height: '70%', position: 'relative', overflow: 'hidden' }}>
                    <Image
                        source={require('./images/nfcPic.png')}
                        style={[styles.imageOne,]}
                        resizeMode="contain"
                    />
                </View>
            </View>
            <View style={styles.btnDiv}>

                {!modalVisible && (
                    <TouchableOpacity
                        style={styles.button}
                        onPress={startNfcScan}
                    >
                        <Text style={styles.buttonText}>Connect Your Card</Text>
                    </TouchableOpacity>
                )}
            </View>

            {/* Modal for NFC Scanning */}
            <Modal visible={modalVisible} transparent={true}>
                <View style={styles.modalBackground}>
                    <View style={styles.modalContent}>
                        <Animated.Image
                            source={require('./images/card1.png')}
                            style={[styles.image, { transform: [{ translateX: position }] }]}
                            resizeMode="contain"
                        />
                        <Text style={styles.modalText}>NFC in Progress Please Scan a Card</Text>
                        <TouchableOpacity
                            style={[styles.button, { backgroundColor: 'red', borderColor: 'red', marginTop: 20, elevation: 20 }]}
                            onPress={terminateScan}
                        >
                            <Text style={styles.buttonText}>Cancel</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </Modal>
        </View>
    );
};

export default NfcScan;

const styles = StyleSheet.create({
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#f0f4f7',
    },
    loadingText: {
        marginTop: 10,
        fontSize: 16,
        color: '#1677FF',
    },
    wrapper: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#f5f5f5',
    },
    button: {
        backgroundColor: '#4995FF',
        paddingVertical: 15,
        paddingHorizontal: 30,
        borderRadius: 10,
        borderColor: '#1677ff',
        borderWidth: 1,
        elevation: 10,
    },
    btnDiv: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#1677ff',
        width: '100%',
        borderTopLeftRadius: 30,
        borderTopRightRadius: 30,

    },
    buttonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 16,

    },
    modalBackground: {
        flex: 1,
        justifyContent: 'flex-end',
        alignItems: 'end',
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    modalContent: {

        backgroundColor: 'white',
        padding: 20,
        borderRadius: 10,
        alignItems: 'center',
    },
    modalText: {
        marginTop: 20,
        fontSize: 16,
        color: '#333',
        textAlign: 'center',
    },
    image: {
        width: 150,
        height: 150,
    },
    imageOne: {
        width: 450,
        height: 350,
        resizeMode: "contain",
    },
});


