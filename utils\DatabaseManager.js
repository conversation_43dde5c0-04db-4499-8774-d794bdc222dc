import { openDatabase } from 'react-native-sqlite-storage';

class DatabaseManager {
  constructor() {
    this.db = null;
    this.isInitialized = false;
  }

  // Singleton pattern to ensure only one database instance
  static getInstance() {
    if (!DatabaseManager.instance) {
      DatabaseManager.instance = new DatabaseManager();
    }
    return DatabaseManager.instance;
  }

  // Initialize database connection
  async initialize() {
    if (this.isInitialized && this.db) {
      return this.db;
    }

    try {
      this.db = openDatabase({ 
        name: 'UserDatabase.db',
        location: 'default',
        createFromLocation: 1
      });
      
      await this.createTables();
      this.isInitialized = true;
      console.log('✅ Database initialized successfully');
      return this.db;
    } catch (error) {
      console.error('❌ Database initialization failed:', error);
      throw error;
    }
  }

  // Create necessary tables
  async createTables() {
    return new Promise((resolve, reject) => {
      this.db.transaction((tx) => {
        // Create Transactions Table
        tx.executeSql(
          `CREATE TABLE IF NOT EXISTS Transactions (
            id INTEGER PRIMARY KEY AUTOINCREMENT, 
            operator_id INTEGER, 
            route_id INTEGER, 
            customer_id INTEGER, 
            serial_no TEXT, 
            name TEXT, 
            quantity REAL, 
            total_price REAL, 
            created_at TEXT, 
            sync INTEGER DEFAULT 0
          )`,
          [],
          () => console.log('✅ Transactions table created'),
          (_, error) => {
            console.error('❌ Transactions table creation failed:', error);
            reject(error);
          }
        );

        // Create Customers Table
        tx.executeSql(
          `CREATE TABLE IF NOT EXISTS Customers (
            id INTEGER PRIMARY KEY, 
            name TEXT, 
            card_id TEXT, 
            serial_no TEXT, 
            balance REAL
          )`,
          [],
          () => console.log('✅ Customers table created'),
          (_, error) => {
            console.error('❌ Customers table creation failed:', error);
            reject(error);
          }
        );

        // Create Products Table
        tx.executeSql(
          `CREATE TABLE IF NOT EXISTS Products (
            id INTEGER PRIMARY KEY, 
            name TEXT, 
            price REAL, 
            quantity REAL
          )`,
          [],
          () => {
            console.log('✅ Products table created');
            resolve();
          },
          (_, error) => {
            console.error('❌ Products table creation failed:', error);
            reject(error);
          }
        );
      });
    });
  }

  // Get database instance
  getDatabase() {
    if (!this.isInitialized || !this.db) {
      throw new Error('Database not initialized. Call initialize() first.');
    }
    return this.db;
  }

  // Execute SQL with proper error handling and connection management
  async executeSql(sql, params = []) {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not initialized'));
        return;
      }

      this.db.transaction((tx) => {
        tx.executeSql(
          sql,
          params,
          (_, results) => resolve(results),
          (_, error) => {
            console.error('❌ SQL execution failed:', error);
            reject(error);
          }
        );
      });
    });
  }

  // Batch operations for better performance
  async executeBatch(operations) {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not initialized'));
        return;
      }

      this.db.transaction((tx) => {
        let completed = 0;
        const total = operations.length;

        operations.forEach(({ sql, params = [] }) => {
          tx.executeSql(
            sql,
            params,
            () => {
              completed++;
              if (completed === total) {
                resolve();
              }
            },
            (_, error) => {
              console.error('❌ Batch operation failed:', error);
              reject(error);
            }
          );
        });
      });
    });
  }

  // Close database connection
  async close() {
    if (this.db) {
      try {
        await this.db.close();
        this.db = null;
        this.isInitialized = false;
        console.log('✅ Database connection closed');
      } catch (error) {
        console.error('❌ Error closing database:', error);
      }
    }
  }

  // Clean up old data to prevent memory bloat
  async cleanupOldData(daysToKeep = 30) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
    const cutoffString = cutoffDate.toISOString().split('T')[0];

    try {
      await this.executeSql(
        'DELETE FROM Transactions WHERE sync = 1 AND created_at < ?',
        [cutoffString]
      );
      console.log(`✅ Cleaned up old synced transactions before ${cutoffString}`);
    } catch (error) {
      console.error('❌ Cleanup failed:', error);
    }
  }
}

export default DatabaseManager;
