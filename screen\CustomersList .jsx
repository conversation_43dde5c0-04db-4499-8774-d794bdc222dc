
import { ActivityIndicator, Alert, FlatList, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React, { useEffect, useRef, useState } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useStateContext } from './StateProvider';
import { openDatabase } from 'react-native-sqlite-storage';
import BackgroundFetch from 'react-native-background-fetch';
import NetInfo from '@react-native-community/netinfo';
import LoadingComponent from './LoadingComponent';
import Config from 'react-native-config';
import NfcManager, { NfcTech } from 'react-native-nfc-manager';
import { useNavigation } from '@react-navigation/native';
import MaterialCommunityIcons from 'react-native-vector-icons/dist/MaterialCommunityIcons';
import { rgbaColor } from 'react-native-reanimated/lib/typescript/Colors';



const db = openDatabase({ name: 'UserDatabase.db' });

const CustomersList = () => {
    const [customers, setCustomers] = useState([]);
    const [loading, setLoading] = useState(true);
    const [findUserLoading, setFindUserLoading] = useState(false);
     const [highlightedUserId, setHighlightedUserId] = useState(null);
    const flatListRef = useRef(null);
    const { transactionLoad, setTransactionLoad, transactionAndCustomers } = useStateContext();
    const navigation = useNavigation();

    const apiBaseUrl = Config.API_BASE_URL;


    // 🔥 Fetch Customers from SQLite
    const fetchCustomersFromDB = () => {
        db.transaction((tx) => {
            tx.executeSql(
                'SELECT * FROM Customers',
                [],
                (tx, results) => {
                    let temp = [];
                    for (let i = 0; i < results.rows.length; ++i) {
                        temp.push(results.rows.item(i));
                    }
                    setCustomers(temp);
                    setLoading(false);
                },
                (_, error) =>
                    Alert.alert('Error', error || 'Failed to load customers. Please try again.')
            );
        });
    };

    // 🔄 Insert or Update Customers in SQLite
    const insertOrUpdateCustomers = (customers) => {
        db.transaction((tx) => {
            customers.forEach((customer) => {
                tx.executeSql(
                    `INSERT OR REPLACE INTO Customers (id, name, card_id, serial_no, balance) VALUES (?, ?, ?, ?, ?)`,
                    [customer.id, customer.name, customer.card_id, customer.serial_no, parseFloat(customer.balance)],
                    // () => console.log(`✅ Customers List inserted/updated`),
                    (_, error) => console.error('❌ Error inserting customer:', error)
                );
            });
        });
    };



    const syncTransactions = async (unsyncedTransactions) => {
        const B_TOKEN = await AsyncStorage.getItem('token');
        const token = JSON.parse(B_TOKEN);

        try {
            const batchSize = 10; // ✅ Send transactions in batches of 10
            for (let i = 0; i < unsyncedTransactions.length; i += batchSize) {
                let batch = unsyncedTransactions.slice(i, i + batchSize);

                let response = await fetch(`${apiBaseUrl}/transactions/sync`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({ transactions: batch }),
                });

                const result = await response.json();
                if (result.success) {
                    db.transaction(tx => {
                        batch.forEach(transaction => {
                            tx.executeSql(
                                "UPDATE Transactions SET sync = 1 WHERE id = ?",
                                [transaction.id],
                                () =>
                                    setTransactionLoad(true),
                                (_, error) =>
                                    Alert.alert("Error updating sync status in SQLite:", error)
                            );
                        });
                    });
                } else {
                    return false; // If sync fails, return false
                }
            }

            return true; // If all transactions synced successfully, return true
        } catch (error) {
            Alert.alert('Error', 'Failed to sync transactions. Please try again.');
            setLoading(false);

            return false;
        }
    };



    const refreshCustomers = async () => {
        //  Step 1: Check internet connection
        const netInfo = await NetInfo.fetch();
        if (!netInfo.isConnected) {
            Alert.alert("No Internet", "Please check your internet connection and try again.");
            return;
        }
        setLoading(true);

        //  Step 2: Check for unsynced transactions
        db.transaction((tx) => {
            tx.executeSql(
                "SELECT * FROM Transactions WHERE sync = 0",
                [],
                async (_, { rows }) => {
                    const unsyncedTransactions = [];
                    for (let i = 0; i < rows.length; i++) {
                        unsyncedTransactions.push(rows.item(i));
                    }

                    if (unsyncedTransactions.length > 0) {

                        const syncSuccess = await syncTransactions(unsyncedTransactions);
                        if (!syncSuccess) {
                            setLoading(false);
                            Alert.alert("Failed to sync transactions. Please try again later.");
                            return;
                        }
                    }

                    const B_TOKEN = await AsyncStorage.getItem('token');
                    const token = JSON.parse(B_TOKEN);


                    // ✅ Step 3: Refresh customers
                    try {
                        const storedCardId = await AsyncStorage.getItem('userCardId');
                        if (!storedCardId) {
                            return;
                        }

                        // const cardId = JSON.parse(storedCardId);
                        let response = await fetch(`${apiBaseUrl}/customers`, {
                            method: 'GET',
                            headers: {
                                'Content-Type': 'application/json',
                                'Accept': 'application/json',
                                'Authorization': `Bearer ${token}`
                            },

                        });

                        const result = await response.json();

                        if (result.success) {
                            insertOrUpdateCustomers(result.customers);
                            setCustomers(result.customers);
                            setLoading(false);

                        } else {
                            setLoading(false);

                        }
                    } catch (error) {
                        Alert.alert('Error', error.message || 'Failed to refresh customers. Please try again.');
                        setLoading(false);

                    }
                },
                (_, error) => {
                    Alert.alert('❌ Error checking unsynced transactions:', error);
                    setLoading(false);

                }
            );
        });
    };

    // 🔄 Run Fetch on Screen Load & Background
    useEffect(() => {
        fetchCustomersFromDB();
    }, [transactionLoad]);

    const startCustomerNfcCard = async () => {
        // setLoading(true);
        setFindUserLoading(true);
        try {
            await NfcManager.requestTechnology(NfcTech.Ndef);
            const tag = await NfcManager.getTag();
            const scanCardId = tag?.id || tag?.techList?.[0];
            const findUser = customers.find((c) => c.card_id === scanCardId);
            if (findUser) {
                NfcManager.cancelTechnologyRequest();
                // design alert the border radius 10 and background color #1677FF
                Alert.alert('User Found', `Serial No: ${findUser.serial_no}\nName: ${findUser.name}\nBalance: Rs ${findUser.balance}`, [
                    {
                        text: 'OK',
                        onPress: () => {
                            setFindUserLoading(false);
                            return;
                        }
                    }
                ]);

            } else {
                Alert.alert('Card Not Found', 'This card is not registered in the system.');
                setFindUserLoading(false);
                NfcManager.cancelTechnologyRequest();
            }

        } catch (error) {
            // console.warn('❌ NFC Error:', error);
            NfcManager.cancelTechnologyRequest();
            Alert.alert('NFC Error', error?.message || error?.toString?.() || 'Unknown NFC error');
            setFindUserLoading(false);

        } finally {
            await NfcManager.cancelTechnologyRequest();
            setFindUserLoading(false);

        }
    };




    // terminate NFC manager on unmount
    const terminateScan = async () => {
        try {
            NfcManager.cancelTechnologyRequest();
            await NfcManager.setEventListenerEnabled(false);
            await NfcManager.stop();
            setFindUserLoading(false);

        } catch (error) {
            // console.warn('❌ NFC Cleanup Error:', error);
        }
    };

    if (loading) {
        return (
            <LoadingComponent />
        );
    }

    if (findUserLoading) {
        return (
            <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color="#1677FF" />
                <Text style={styles.loadingText}>Searching for User...</Text>

                <TouchableOpacity onPress={() => terminateScan()} style={styles.cancelButton}>
                    <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>
            </View>
        );
    }

    return (
        <View style={{ flex: 1, backgroundColor: '#8EC3E6' }}>
            {/* Header */}
            <View style={{ padding: 10, backgroundColor: '#1677FF', alignItems: 'center', flexDirection: 'row', justifyContent: 'space-between' }}>
                {/* left Arrow  open sideBAr  */}
                {/* <TouchableOpacity onPress={() => navigation.openDrawer()} style={{}}> */}
                <TouchableOpacity onPress={() => navigation.navigate('Home')} style={{ padding: 5, marginRight: 10 }}>

                    <Text style={{ color: 'white', fontSize: 18 }}><MaterialCommunityIcons name="arrow-left" color="#fff" size={30} /></Text>
                </TouchableOpacity>
                <Text style={{ color: 'white', fontSize: 18, fontWeight: 'bold' }}>Customers List</Text>
                <Text style={{ color: 'white', fontSize: 16, }}>{`( ${customers.length} )`}</Text>
            </View>
            <View>
                {/* Tow buttons (REFRESH) And (Find User) */}
                <View style={{ flexDirection: 'row', justifyContent: 'space-between', padding: 10 }}>
                    <TouchableOpacity onPress={refreshCustomers} style={styles.refreshButton}>
                        <Text>
                            <MaterialCommunityIcons name="refresh" color="#fff" size={20} />
                        </Text>
                        <Text style={{ color: 'white', alignItems: 'center', justifyContent: 'center' }}>Refresh User</Text>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={startCustomerNfcCard} style={styles.refreshButton}>
                        <Text>
                            <MaterialCommunityIcons name="magnify" color="#fff" size={20} />
                        </Text>
                        <Text style={{ color: 'white', alignItems: 'center', justifyContent: 'center' }}>Find User</Text>
                    </TouchableOpacity>
                </View>
            </View>




            {/* Customers List */}
            <View style={{ flex: 1, backgroundColor: 'white', borderTopLeftRadius: 20, borderTopRightRadius: 20, paddingTop: 10 }}>

                <FlatList
                    ref={flatListRef}
                    data={customers}
                    keyExtractor={(item) => item.id.toString()}
                    renderItem={({ item }) => (
                        <View style={[styles.transactionCard,
                         { backgroundColor: item.id === highlightedUserId ? '#ffe066' : '#fff' }
                         ]}>
                            <View style={{ flex: 1, padding: 3 }}>
                                <View style={[styles.row, { paddingLeft: 5 }]}>
                                    <Text style={[styles.text, { color: 'rgba(255, 0, 128, 0.49)' }]}>Sr_No:</Text>
                                    <Text style={[styles.text, { backgroundColor: 'rgba(255, 0, 128, 0.34)', paddingHorizontal: 5, borderRadius: 5, color: 'white' }]}>{item.serial_no}</Text>
                                </View>
                                <View style={[styles.row, { paddingLeft: 5, marginTop: 2 }]}>
                                    <Text style={[styles.text, { color: 'rgb(255, 153, 0)' }]}>Name:</Text>
                                    <Text style={[styles.text, { backgroundColor: 'rgba(255, 153, 0, 0.42)', paddingHorizontal: 5, borderRadius: 5, color: 'gray' }]}>{item.name}</Text>
                                </View>
                                <View style={[styles.row, { paddingLeft: 5, marginTop: 2 }]}>
                                    <Text style={[styles.text, { color: 'green' }]}>Balance:</Text>
                                    <Text style={[styles.text, { opacity: 1, backgroundColor: 'rgba(0, 128, 0, 0.5)', paddingHorizontal: 5, borderRadius: 5, color: 'white' }]}>RS {item.balance}</Text>
                                </View>
                            </View>
                        </View>
                    )}
                    getItemLayout={(data, index) => (
        { length: 70, offset: 70 * index, index }
    )}
    onScrollToIndexFailed={({ index, highestMeasuredFrameIndex, averageItemLength }) => {
        setTimeout(() => {
            if (flatListRef.current) {
                flatListRef.current.scrollToIndex({
                    index: Math.max(0, highestMeasuredFrameIndex),
                    animated: true,
                });
            }
        }, 500);
    }}
                />
            </View>
        </View>
    );
};

export default CustomersList;

const styles = StyleSheet.create({
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#f0f4f7',
    },
    loadingText: {
        marginTop: 10,
        fontSize: 16,
        color: '#1677FF',
    },
    cancelButton: {
        marginTop: 20,
        padding: 10,
        paddingHorizontal: 20,
        alignItems: 'center',
        backgroundColor: '#FF4D4D',
        borderRadius: 5,
    },
    cancelButtonText: {
        color: 'white',
        fontWeight: 'bold',
    },
    transactionCard: {
        width: '97%',
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginHorizontal: 'auto',
        padding: 5,
        borderBottomWidth: 1,
        borderBottomColor: '#ccc',
        height: 70,


    },
    row: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    text: {
        fontSize: 12,
        fontWeight: 'semibold',
        color: 'black',
    },
    refreshButton: {
        flexDirection: 'row',
        justifyContent: 'center',
        backgroundColor: '#1677FF',
        borderRadius: 10,
        padding: 1,
        paddingVertical: 10,
        width: '40%',
        alignItems: 'center',
    },
});

