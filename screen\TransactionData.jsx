

import {
  ActivityIndi<PERSON><PERSON>,
  <PERSON><PERSON>,
  FlatList,
  RefreshControl,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, { useEffect, useState } from 'react';
import { openDatabase } from 'react-native-sqlite-storage';
import { useNavigation } from '@react-navigation/native';
import NetInfo from '@react-native-community/netinfo';
import AsyncStorage from '@react-native-async-storage/async-storage';
import LoadingComponent from './LoadingComponent';
import Config from 'react-native-config';


const db = openDatabase({ name: 'UserDatabase.db' });

const TransactionData = () => {
  const navigation = useNavigation();
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

   const apiBaseUrl = Config.API_BASE_URL;
  

  // Fetch transactions
  const fetchTransactionsFromDB = async () => {
    try {
      setLoading(true);
      await new Promise((resolve, reject) => {
        db.transaction((tx) => {
          tx.executeSql(
            'SELECT * FROM Transactions ORDER BY id DESC',
            [],
            (_, results) => {
              const temp = [];
              for (let i = 0; i < results.rows.length; ++i) {
                temp.push(results.rows.item(i));
              }
              setTransactions(temp);
              resolve();
            },
            (_, error) => reject(error)
          );
        });
      });
    } catch (error) {
      console.error('Fetch transactions error:', error);
      Alert.alert('Error', 'Failed to load transactions. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTransactionsFromDB();
  }, []);

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchTransactionsFromDB();
    setRefreshing(false);
  };

  // Sync all unsynced transactions
  const syncAllTransactions = async () => {
    const netInfo = await NetInfo.fetch();
    if (!netInfo.isConnected) {
      console.log("⚠️ No internet connection. Skipping Transactions.");
      Alert.alert('⚠️ No internet connection.', ' plz connect your internet ');
      return;
    }
    const unsynced = transactions.filter((item) => !item.sync);
    if (unsynced.length === 0) {
      Alert.alert('No Unsynced Transactions', 'All transactions are already synced.');
      return;
    }

    try {
      setLoading(true);
      const rawToken = await AsyncStorage.getItem('token');
       const token = rawToken.replace(/"/g, '');
      const response = await fetch(`${apiBaseUrl}/transactions/sync`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': `Bearer ${token}`
      },
        body: JSON.stringify({ transactions: unsynced }),
      });

      const result = await response.json();
      console.log('Sync Response:', result);

      if (result.success) {
        db.transaction((tx) => {
          unsynced.forEach((item) => {
            tx.executeSql(
              'UPDATE Transactions SET sync = 1 WHERE id = ?',
              [item.id],
              () => console.log(`Transaction ${item.id} synced ✅`),
              (_, error) => console.log('Error updating sync:', error)
            );
          });
        });
        await fetchTransactionsFromDB();
        Alert.alert('Success', 'Unsynced transactions synced!');
      } else {
        Alert.alert('Sync Failed', 'Could not sync transactions.');
      }
    } catch (error) {
      Alert.alert('Error', 'Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Delete all synced transactions
  const deleteAllTransaction = () => {
    db.transaction((tx) => {
      tx.executeSql(
        'SELECT COUNT(*) AS count FROM Transactions WHERE sync = 1',
        [],
        (_, { rows }) => {
          const syncCount = rows.item(0).count;
          if (syncCount === 0) {
            Alert.alert('No Synced Transactions', 'Nothing to delete.');
            return;
          }

          Alert.alert(
            'Confirm Delete',
            `Delete ${syncCount} synced transactions?`,
            [
              { text: 'Cancel', style: 'cancel' },
              {
                text: 'OK',
                onPress: () => {
                  db.transaction((tx) => {
                    tx.executeSql(
                      'DELETE FROM Transactions WHERE sync = 1',
                      [],
                      () => {
                        setTransactions((prev) => prev.filter((t) => !t.sync));
                        Alert.alert('Success', 'Synced transactions deleted!');
                      },
                      (_, error) => console.log('Delete error:', error)
                    );
                  });
                },
              },
            ]
          );
        }
      );
    });
  };

  if (loading && !refreshing) {
    return (
      <LoadingComponent/>
    )
  }

  return (
    <View style={{ flex: 1, backgroundColor: 'white' }}>
      <View style={[styles.row, { padding: 20 }]}>
        <Text style={{ fontWeight: 'bold', color: 'black' }}>Total Transactions:</Text>
        <Text style={{ fontWeight: 'bold', color: 'black' }}> {transactions.length}</Text>
      </View>

      <FlatList
        data={transactions}
        keyExtractor={(item) => item.id.toString()}
        contentContainerStyle={{ paddingBottom: 60 }}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        renderItem={({ item }) => (
          <View
            style={[
              styles.transactionCard,
              { backgroundColor: item.sync ? 'rgba(0,128,0,0.2)' : 'rgba(255,0,0,0.2)' },
            ]}
          >
            <View style={{ flex: 1, padding: 2 }}>
              <View style={styles.row}>
                <Text style={styles.text}>Sr No: {item.serial_no}</Text>
                <Text style={styles.text}>{item.name}</Text>
              </View>
              <View style={styles.row}>
                <Text style={styles.text}>Qty: {item.quantity}</Text>
                <Text style={styles.text}>Price: {item.total_price}</Text>
              </View>
              <View style={styles.row}>
                <Text style={styles.text}>Date: {item.created_at}</Text>
                <Text style={[styles.text, { color: item.sync ? 'green' : 'red' }]}>
                  {item.sync ? 'Synced' : 'Pending'}
                </Text>
              </View>
            </View>
          </View>
        )}
        ListEmptyComponent={
          <View style={styles.noTransactionCard}>
            <Text style={styles.noTransactionText}>No Transactions Found</Text>
          </View>
        }
      />

      <View style={styles.buttonContainer}>
        <TouchableOpacity
          disabled={transactions.length === 0 || loading}
          onPress={syncAllTransactions}
          style={[styles.button,{backgroundColor: transactions.length === 0 || loading ? '#8EC3E6' : '#1677ff',},]}>
          <Text style={styles.buttonText}>{loading ? 'Syncing...' : 'Sync All'}</Text>
        </TouchableOpacity>
        <TouchableOpacity
          disabled={transactions.length === 0 || loading}
          onPress={deleteAllTransaction}
          style={[ styles.button, { backgroundColor: transactions.length === 0 || loading ? '#FF8886' : 'red', }, ]} >
          <Text style={styles.buttonText}>{loading ? 'Deleting...' : 'Delete All'}</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default TransactionData;

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f4f7',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#1677FF',
  },
  noTransactionCard: {
    width: '90%',
    height: 200,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
    elevation: 3,
    backgroundColor: '#fff',
    marginHorizontal: 'auto',
    marginTop: 20,
  },
  noTransactionText: {
    fontSize: 16,
    color: 'gray',
  },
  transactionCard: {
    width: '90%',
    padding: 5,
    borderRadius: 10,
    elevation: 3,
    marginHorizontal: 'auto',
    marginTop: 10,
    backgroundColor: '#fff',
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 2,
  },
  text: {
    fontSize: 12,
    color: 'black',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 20,
  },
  button: {
    borderRadius: 10,
    padding: 10,
    width: '45%',
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
  },
});
