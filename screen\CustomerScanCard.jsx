
import { ActivityIndicator, <PERSON><PERSON>, BackHandler, Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React, { useEffect, useState } from 'react'
import { useNavigation, useRoute } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import SunmiPrinter from '@hendrysetiadi/react-native-sunmi-printer';
import { useStateContext } from './StateProvider';
import MaterialCommunityIcons from 'react-native-vector-icons/dist/MaterialCommunityIcons';
import { openDatabase } from 'react-native-sqlite-storage';

var db = openDatabase({ name: 'UserDatabase.db' });

const CustomerScanCard = () => {
  const route = useRoute();
  const { newTransaction } = route.params; // Get transaction data from route params
  const navigation = useNavigation();
  const [loading, setLoading] = useState(false);
  const { setTransactionLoad } = useStateContext();
  
  // Extract data from transaction
  const customerName = newTransaction?.name || 'Customer';
  const serialNo = newTransaction?.serial_no || 'N/A';
  const quantity = newTransaction?.quantity || 0;
  const totalPrice = newTransaction?.total_price || 0;
  const remainingBalance = route.params?.remainingBalance || 0;
  const scanTime = newTransaction?.created_at?.split(' ')[1] || new Date().toLocaleTimeString();
  const scanDate = newTransaction?.created_at?.split(' ')[0] || new Date().toLocaleDateString();

  useEffect(() => {
    const backAction = () => {
      navigation.navigate('Home');
      return true;
    };

    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction
    );
    
    return () => {
      backHandler.remove();
    };
  }, []);

  const printReceipt = async () => {
    try {
      setLoading(true);
      await SunmiPrinter.initPrinter();
      await SunmiPrinter.setAlignment(1);
      await SunmiPrinter.printText('\n', 50);
      await SunmiPrinter.printText('******** RECEIPT ********\n', 50);
      await SunmiPrinter.printText('\n', 20);
      await SunmiPrinter.printText(`Mil Farm Rwp\n`, 10);
      await SunmiPrinter.setAlignment(0);
      await SunmiPrinter.printText(`Serial No: ${serialNo}\n`, 10);
      await SunmiPrinter.printText(`Name: ${customerName}\n`, 10);
      await SunmiPrinter.printText(`Quantity: ${quantity} Ltr\n`, 10);
      await SunmiPrinter.printText(`Price: RS ${totalPrice}\n`, 10);
      await SunmiPrinter.printText(`Remaining Balance: RS ${remainingBalance}\n`, 10);
      await SunmiPrinter.printText(`Transaction Time: ${scanTime}\n`, 10);
      await SunmiPrinter.printText(`Transaction Date: ${scanDate}\n`, 10);
      await SunmiPrinter.printText('\n', 10);
      await SunmiPrinter.printText('************************\n', 50);
      await SunmiPrinter.printText('\n', 20);
      await SunmiPrinter.printText('\n', 20);
      await SunmiPrinter.printText('\n', 50);

      Alert.alert('Print Successful', 'Receipt printed successfully.');
      setLoading(false);
      navigation.navigate('Home');
    } catch (error) {
      Alert.alert('Print Error', error.message);
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#1677FF" />
        <Text style={styles.loadingText}>Printing...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.navigate('Home')} style={styles.backButton}>
          <MaterialCommunityIcons name="arrow-left" color="#fff" size={30} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Transaction Receipt</Text>
      </View>
      
      <View style={styles.receiptContainer}>
        <Text style={styles.receiptTitle}>Transaction Details</Text>
        
        <View style={styles.receiptRow}>
          <Text style={styles.receiptLabel}>Serial No:</Text>
          <Text style={styles.receiptValue}>{serialNo}</Text>
        </View>
        
        <View style={styles.receiptRow}>
          <Text style={styles.receiptLabel}>Customer:</Text>
          <Text style={styles.receiptValue}>{customerName}</Text>
        </View>
        
        <View style={styles.receiptRow}>
          <Text style={styles.receiptLabel}>Quantity:</Text>
          <Text style={styles.receiptValue}>{quantity} Ltr</Text>
        </View>
        
        <View style={styles.receiptRow}>
          <Text style={styles.receiptLabel}>Price:</Text>
          <Text style={styles.receiptValue}>RS {totalPrice}</Text>
        </View>
        
        <View style={styles.receiptRow}>
          <Text style={styles.receiptLabel}>Remaining Balance:</Text>
          <Text style={styles.receiptValue}>RS {remainingBalance}</Text>
        </View>
        
        <View style={styles.receiptRow}>
          <Text style={styles.receiptLabel}>Date:</Text>
          <Text style={styles.receiptValue}>{scanDate}</Text>
        </View>
        
        <View style={styles.receiptRow}>
          <Text style={styles.receiptLabel}>Time:</Text>
          <Text style={styles.receiptValue}>{scanTime}</Text>
        </View>
      </View>
      
      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.printButton} onPress={printReceipt}>
          <MaterialCommunityIcons name="printer" color="#fff" size={24} />
          <Text style={styles.buttonText}>Print Receipt</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.homeButton} 
          onPress={() => navigation.navigate('Home')}
        >
          <MaterialCommunityIcons name="home" color="#fff" size={24} />
          <Text style={styles.buttonText}>Go to Home</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    height: 60,
    backgroundColor: '#1677FF',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
  },
  backButton: {
    marginRight: 15,
  },
  headerTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  receiptContainer: {
    margin: 15,
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 20,
    elevation: 3,
  },
  receiptTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
    color: '#1677FF',
  },
  receiptRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  receiptLabel: {
    fontSize: 16,
    color: '#555',
    fontWeight: '500',
  },
  receiptValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 20,
    paddingHorizontal: 15,
  },
  printButton: {
    backgroundColor: '#1677FF',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    elevation: 2,
    flex: 1,
    marginRight: 10,
  },
  homeButton: {
    backgroundColor: '#4CAF50',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    elevation: 2,
    flex: 1,
    marginLeft: 10,
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    marginLeft: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.8)',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#1677FF',
  },
});

export default CustomerScanCard;