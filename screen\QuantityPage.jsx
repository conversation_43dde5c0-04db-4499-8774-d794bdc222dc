import { ActivityIndicator, <PERSON><PERSON>, <PERSON>, BackHandler, Dimensions, Image, Modal, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { useFocusEffect, useIsFocused, useNavigation } from '@react-navigation/native';
import MaterialCommunityIcons from 'react-native-vector-icons/dist/MaterialCommunityIcons';
import NfcManager, { NfcTech } from 'react-native-nfc-manager';
import { openDatabase } from 'react-native-sqlite-storage';
import Carousel from 'react-native-reanimated-carousel';
import LoadingComponent from './LoadingComponent';
import Config from 'react-native-config';

var db = openDatabase({ name: 'UserDatabase.db' });



const QuantityPage = () => {

  const [products, setProducts] = useState([]);
  const [loader, setLoader] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedQuantity, setSelectedQuantity] = useState(null);
  const [selectedLiter, setSelectedLiter] = useState(null);
  const [btnDisabled, setBtnDisabled] = useState(false);
  const [loading, setLoading] = useState(false);

  const apiBaseUrl = Config.API_BASE_URL;




  const navigation = useNavigation();

  const width = Dimensions.get('window').width;
  const list = [
    {
      id: 1,
      image: require('./images/milk1.jpg'),
    },
    {
      id: 2,
      image: require('./images/milk2.jpg'),
    },
    {
      id: 3,
      image: require('./images/milk3.jpg'),
    },
    {
      id: 4,
      image: require('./images/milk4.jpg'),
    },
    {
      id: 5,
      image: require('./images/milk5.jpg'),
    },
    {
      id: 6,
      image: require('./images/milk6.jpg'),
    },
    {
      id: 7,
      image: require('./images/pxfuel.jpg'),
    },
  ];




  ///// useEffect GET PRODUCTS FROM ASYNC STORAGE
  useEffect(() => {

    const getProducts = async () => {
      setLoader(true)
      try {
        const storProducts = await AsyncStorage.getItem('products');
        setProducts(JSON.parse(storProducts))
      } catch {
        Alert.alert('Error', 'Failed to load products.');
      } finally {
        setLoader(false)
      }
    };

    getProducts();
  }, []);

  ///////// useEffect MANAGE BACK BUTTON 
  useEffect(() => {

    const backAction = () => {
      terminateScan(); // Stop NFC when back button is pressed
      return true; // Prevent default back action
    };

    // Add Event Listener for Back Button
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction
    );
    return () => {
      backHandler.remove();
    };
  }, []);

  //////// INSERT TRANSACTION INTO SQLITE
  const insertTransaction = (transaction) => {
    db.transaction(tx => {
      tx.executeSql(
        `INSERT INTO Transactions 
              (operator_id, route_id, customer_id, serial_no, name, product_id, 
              quantity, status, created_at, total_price, sync) 
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          transaction.operator_id,
          transaction.route_id,
          transaction.customer_id,
          transaction.serial_no,
          transaction.name,
          transaction.product_id,
          transaction.quantity,
          transaction.status,
          transaction.created_at,
          transaction.total_price,
          transaction.sync ? 1 : 0, // Convert boolean to 1 (true) or 0 (false)
        ],
        () => setLoading(true),
        (_, error) => Alert.alert("Error inserting transaction into SQLite:", error)
      );
    });
  };

  ////////// FUNCTION NFC MANAGER
  const startCustomerNfcCard = async (item) => {

    setSelectedQuantity(item.quantity);
    setSelectedLiter(item.total_price);
    setModalVisible(true);
    setLoading(true);
    try {
      await NfcManager.requestTechnology(NfcTech.Ndef);
      const tag = await NfcManager.getTag();
      setBtnDisabled(true);
      // console.log('NFC Tag: ', tag);
      const scanCardId = tag?.id || tag?.techList?.[0];
      // console.log('Card idddddddddd', scanCardId)


      const operatorId = await AsyncStorage.getItem('operator_id');
      let operator_Idd = JSON.parse(operatorId);
      const B_TOKEN = await AsyncStorage.getItem('token');
      let token = JSON.parse(B_TOKEN);
      // console.log('Bearer tokeeeennnnn',token)

      const now = new Date();
      const formattedDate = now.toLocaleDateString();
      const formattedTime = now.toLocaleTimeString();

      //  Fetch customer from SQLite using card ID 
      db.transaction((tx) => {
        tx.executeSql(
          "SELECT * FROM Customers WHERE card_id = ?",
          [scanCardId],
          (tx, results) => {
            if (results.rows.length === 0) {
              Alert.alert('Error', 'Card not recognized. Please scan again.');
              NfcManager.cancelTechnologyRequest();
              // navigation.navigate('Home');
              setModalVisible(false);
              return;
            }

            const foundUser = results.rows.item(0);
            // console.log('Found Customer:', foundUser);

            const newBalance = foundUser.balance - item.total_price;
            if (newBalance < 0) {
              Alert.alert('Error', `Insufficient balance. Current balance: ${foundUser.balance}`);
              NfcManager.cancelTechnologyRequest();
              // navigation.navigate('Home');
              setModalVisible(false);
              return;
            }

            //  Send Transaction to API
            fetch(`${apiBaseUrl}/transactions/create`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Authorization': `Bearer ${token}`
              },
              body: JSON.stringify({
                operator_id: operator_Idd,
                customer_id: foundUser.id,
                product_id: item.id,
                quantity: item.quantity,
                status: "accepted",
                created_at: `${formattedDate} ${formattedTime}`,
              }),
            })
              .then(response => response.json())
              .then(result => {
                const isDataSaved = result.success || false;
                // console.log('Sync Transaction API Response:', result);

                const newTransaction = {
                  operator_id: operator_Idd,
                  route_id: 1,
                  customer_id: foundUser.id,
                  serial_no: foundUser.serial_no,
                  name: foundUser.name,
                  product_id: item.id,
                  quantity: item.quantity,
                  status: 'accepted',
                  created_at: `${formattedDate} ${formattedTime}`,
                  total_price: item.total_price,
                  sync: isDataSaved ? 1 : 0,
                };

                // console.log('🆕 New Transaction:', newTransaction);
                insertTransaction(newTransaction);

                // ✅ Update Customer Balance in SQLite
                db.transaction((tx) => {
                  tx.executeSql(
                    "UPDATE Customers SET balance = ? WHERE card_id = ?",
                    [newBalance, scanCardId],
                    () => console.log(`✅ Updated balance for ${foundUser.name}: ${newBalance}`),
                    (_, error) => console.log("❌ Error updating customer balance:", error)
                  );
                });

                // ✅ UI Final Steps
                NfcManager.cancelTechnologyRequest();
                // Navigate to receipt page with transaction data
                navigation.navigate('CustomerScanCard', {
                  newTransaction,
                  remainingBalance: newBalance
                });
                // setModalVisible(false);
              })
              .catch(error => {
                console.log('❌ Network Error, saving transaction as unsynced.');

                // Save locally with sync: 0
                const newTransaction = {
                  operator_id: operator_Idd,
                  route_id: 1,
                  customer_id: foundUser.id,
                  serial_no: foundUser.serial_no,
                  name: foundUser.name,
                  product_id: item.id,
                  quantity: item.quantity,
                  status: 'accepted',
                  created_at: `${formattedDate} ${formattedTime}`,
                  total_price: item.total_price,
                  sync: 0,
                };

                insertTransaction(newTransaction);

                db.transaction((tx) => {
                  tx.executeSql(
                    "UPDATE Customers SET balance = ? WHERE card_id = ?",
                    [newBalance, scanCardId],
                    () => console.log(`✅ Updated balance for ${foundUser.name}: ${newBalance}`),
                    (_, error) => console.log("❌ Error updating balance:", error)
                  );
                });


                NfcManager.cancelTechnologyRequest();
                // Navigate to receipt page with transaction data
                navigation.navigate('CustomerScanCard', {
                  newTransaction,
                  remainingBalance: newBalance
                });
                // setModalVisible(false);
              });
          },
          (_, error) => {
            console.log("❌ Error fetching customer from SQLite:", error);
            NfcManager.cancelTechnologyRequest();
            setModalVisible(false);
          }
        );
      });
    } catch (error) {
      // console.warn('❌ NFC Error:', error);
      NfcManager.cancelTechnologyRequest();
      setModalVisible(false);
      Alert.alert('NFC Error', error?.message || error?.toString?.() || 'Unknown NFC error');

    } finally {
      await NfcManager.cancelTechnologyRequest();
      // setModalVisible(false);
      setBtnDisabled(false);
      setLoading(false);

    }
  };

  ///////// FUNCTION STOP NFC SCAN AND REMOVE MODAL
  const terminateScan = () => {
    try {
      NfcManager.cancelTechnologyRequest();
      // console.log(" NFC Scanning Stopped Successfully");
      setModalVisible(false);
    } catch (error) {
      // console.warn("❌ Error Stopping NFC Scan:", error);
      setModalVisible(false);
    }

  };

  if (loader) {
    return (
      <LoadingComponent />
    );
  }




  return (
    <View style={{ flex: 1, backgroundColor: 'white', }}>
      <View style={{ height: '10%', backgroundColor: '#1677FF', overflow: 'hidden', position: 'relative', }}>
        <View style={{ width: '100%', height: '100%', position: 'relative', overflow: 'hidden' }}>
          <TouchableOpacity onPress={() => navigation.openDrawer()} style={{ position: 'absolute', top: 5, left: 10, zIndex: 50, backgroundColor: '#fff', elevation: 20, borderRadius: 50, padding: 8 }}>
            <Text><MaterialCommunityIcons name="menu" color="#1677FF" size={30} /></Text>
          </TouchableOpacity>
          {/* <Carousel
            width={width}
            height={'100%'}
            data={list}
            autoPlay={true}
            scrollAnimationDuration={5000}
            renderItem={({ item }) => (
              <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', }}>
              <Image
                source={item.image}
                style={[styles.imageCarousel,]}
                resizeMode="cover"
              />
              </View>
            )}
            /> */}

        </View>

      </View>

      <View style={{ flex: 1, backgroundColor: 'white', }}>


        <ScrollView contentContainerStyle={{ paddingBottom: 20 }} >
          <View style={{ width: '90%', marginHorizontal: 'auto', marginVertical: 20, }}>

            <View style={{ display: 'flex', flexDirection: 'row', flexWrap: 'wrap', columnGap: 8, justifyContent: 'space-between', alignItems: 'center', }}>
              {
                products.map((item, index) => (
                  <TouchableOpacity onPress={() => startCustomerNfcCard(item)} key={index} style={styles.card}>
                    <View style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
                      <Text style={{ fontSize: 28, fontWeight: 'bold', color: '#fff', }}>{item.quantity} </Text><Text style={{ color: 'white' }}>Ltr milk</Text>
                    </View>
                    <View style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', justifyContent: 'center', marginVertical: 5 }}>
                      {/* <Text style={styles.whiteText}>{item.base_name}</Text> */}
                      <Text style={styles.whiteText}>RS: {item.total_price}</Text>
                    </View>
                  </TouchableOpacity>
                ))
              }

            </View>
          </View>
        </ScrollView>



      </View>

      {/* <Text style={{ marginTop: 10, textAlign: 'center' }}>Please hold your NFC card to the reader.</Text> */}


      {modalVisible && (
        // <View style={{ flex: 1, width: '100%',height: '100%', justifyContent: 'center', alignItems: 'center', backgroundColor: 'rgba(0,0,0,0.7)' }}>
        <View style={{ flex: 1, position: 'absolute', top: 0, left: 0, width: '100%', height: '100%', justifyContent: 'flex-end', alignItems: '', backgroundColor: 'rgba(0, 0, 0, 0.8)' }}>
          <View style={{ width: '100%', backgroundColor: 'white', padding: 20, borderRadius: 10 }}>
            <Text style={{ fontSize: 18, fontWeight: 'bold', textAlign: 'center', color: 'gray' }}>Ready to Scan</Text>
            <View style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', marginTop: 20 }}>


              <Image
                source={require('./images/card1.png')}
                style={styles.imageOne}
                resizeMode="contain"
              />
            </View>

            <ActivityIndicator style={{ transform: [{ scale: 3 }], marginVertical: 20 }} size="large" color={`${!btnDisabled ? "rgba(68, 126, 250, 0.8)" : 'transparent'}`} />

            <View style={{ marginVertical: 10, display: 'flex', flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingHorizontal: 10 }}>
              <Text style={{ textAlign: 'center', fontWeight: 'bold', }}>Selected Quantity: {selectedQuantity}</Text>
              <Text style={{ textAlign: 'center', fontWeight: 'bold', }}>Total Price: {selectedLiter}</Text>

            </View>
            <TouchableOpacity disabled={btnDisabled} onPress={() => terminateScan()} style={btnDisabled ? styles.disabledButton : styles.blueButton}>
              {!btnDisabled
                ?
                <Text style={styles.buttonText}>Cancel</Text>
                :
                <Text style={styles.buttonText}>Scanning...</Text>
              }
            </TouchableOpacity>
          </View>
        </View>
        // </View>

      )}
    </View>
  )
}

export default QuantityPage

const styles = StyleSheet.create({
  imageCarousel: {
    width: '100%',
    height: '100%',
    resizeMode: "cover",
    // borderBlockColor: 'black',
    // borderRadius: 10,
    // borderWidth: 3,
    zIndex: 1,
  },
  imageOne: {
    width: 60,
    height: 60,
    position: 'absolute',
    // borderWidth: 1,
    // borderColor: 'black',
    // borderRadius: 10,
    top: 10,
    // right: 10,

    // resizeMode: "cover",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',

    // backgroundColor: '#f0f4f7',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#1677FF',
  },
  // });

  blueButton: {
    backgroundColor: '#1677FF',
    padding: 15,
    borderRadius: 10,
    elevation: 3,
  },
  disabledButton: {
    backgroundColor: '#D3D3D3',
    padding: 15,
    borderRadius: 10,
    elevation: 3,
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  productCard: {
    width: '90%',
    elevation: 3,
    borderRadius: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 'auto',
    padding: 15,
    marginTop: 20,
    backgroundColor: '#1677ff',
  },
  quantityBadge: {
    fontSize: 16,
    marginRight: 5,
    paddingHorizontal: 8,
    paddingVertical: 2,
    backgroundColor: '#fff',
    borderRadius: 50,
    color: '#1677FF',
  },
  whiteText: {
    fontSize: 16,
    marginRight: 5,
    color: '#fff',
  },
  card: {
    width: '46%',
    // elevation: 6,
    borderRadius: 10,
    flexDirection: 'column',
    justifyContent: 'space-between',
    marginHorizontal: 'auto',
    padding: 10,
    marginTop: 5,
    backgroundColor: '#1677ff',

    // Add shadow for iOS
    // shadowColor: '#fff',
    // shadowOffset: {
    //   width: 5,
    //   height: 3,
    // },
    shadowOpacity: 0.80,
    // shadowRadius: 5.65,

    // Add elevation for Android
    elevation: 3,

    // Add inner shadow effect with border
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.08)',
  },

})
