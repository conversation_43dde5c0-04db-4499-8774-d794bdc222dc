import { ActivityIndicator, Image, StyleSheet, Text, View } from 'react-native'
import React from 'react'

const LoadingComponent = () => {
    return (
            // source={require('./images/milk2.jpg')}
        <View style={styles.loadingContainer}>
            <Image
                source={require('./images/loader.png')}
                style={styles.imageOne}
                resizeMode="contain"
            />
            <ActivityIndicator size="large" color="#1677FF" />

            {/* <Text style={styles.loadingText}>Loading...</Text> */}
        </View>
    )
}

export default LoadingComponent

const styles = StyleSheet.create({
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#f0f4f7',
    },
    loadingText: {
        marginTop: 10,
        fontSize: 16,
        color: '#1677FF',
    },
    imageOne: {
        width: 140,
        height: 140,
        borderRadius: 20,
    },
})