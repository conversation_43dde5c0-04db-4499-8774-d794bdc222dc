import BackgroundFetch from "react-native-background-fetch";
import AsyncStorage from "@react-native-async-storage/async-storage";
import NetInfo from '@react-native-community/netinfo';
import { openDatabase } from 'react-native-sqlite-storage';
import Config from "react-native-config";
import { Alert } from "react-native";

const db = openDatabase({ name: 'UserDatabase.db' });

 const apiBaseUrl = Config.API_BASE_URL;

const HeadlessTask = async (event) => {
  const net = await NetInfo.fetch();
  if (!net.isConnected) {
    Alert.alert('❌ No internet, stopping...')
    return BackgroundFetch.finish(event.taskId);
  }

  try {
    const unsynced = await new Promise((resolve, reject) => {
      db.transaction((tx) => {
        tx.executeSql(
          "SELECT * FROM Transactions WHERE sync = 0",
          [],
          (_, { rows }) => {
            const arr = [];
            for (let i = 0; i < rows.length; i++) arr.push(rows.item(i));
            resolve(arr);
          },
          (_, err) => reject(err)
        );
      });
    });

    if (unsynced.length > 0) {
      const response = await fetch(`${apiBaseUrl}/transactions/sync`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ transactions: unsynced }),
      });

      const result = await response.json();
      if (result.success) {
        db.transaction((tx) => {
          unsynced.forEach((t) => {
            tx.executeSql(
              "UPDATE Transactions SET sync = 1 WHERE id = ?",
              [t.id]
            );
          });
        });
        // console.log('✅ Transactions synced from headless task');
      }
    }

    const cardId = JSON.parse(await AsyncStorage.getItem('userCardId'));
    const res = await fetch(`${apiBaseUrl}/operator/verify`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ card_id: cardId }),
    });
    const result = await res.json();

    if (result.success) {
      db.transaction((tx) => {
        result.customers.forEach((c) => {
          tx.executeSql(
            `INSERT OR REPLACE INTO Customers (id, name, card_id, serial_no, balance) VALUES (?, ?, ?, ?, ?)`,
            [c.id, c.name, c.card_id, c.serial_no, parseFloat(c.balance)]
          );
        });
      });
      // console.log("✅ Customers updated in headless task");
    }
  } catch (err) {
    // console.error("❌ Headless error:", err);
    Alert.alert("❌ Headless error:", err)
  }

  BackgroundFetch.finish(event.taskId);
};

export default HeadlessTask;
