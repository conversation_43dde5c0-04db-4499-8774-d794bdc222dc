// /**
//  * @format
//  */

// import {AppRegistry} from 'react-native';
// import App from './App';
// import {name as appName} from './app.json';

// AppRegistry.registerComponent(appName, () => App);

import { AppRegistry } from 'react-native';
import App from './App';
import { name as appName } from './app.json';
import { StateProvider } from './screen/StateProvider';
// import { StateProvider } from './screen/StateProvider'; // Import your StateProvider

const RootComponent = () => (
  <StateProvider>
    <App />
  </StateProvider>
);

AppRegistry.registerComponent(appName, () => RootComponent);

// import { AppRegistry } from 'react-native';
// import App from './App';
// import { name as appName } from './app.json';
// import { StateProvider } from './screen/StateProvider';
// import BackgroundFetch from "react-native-background-fetch";
// import { refreshCustomers } from './screen/StateProvider'; // ✅ make sure this exists

// const RootComponent = () => (
//   <StateProvider>
//     <App />
//   </StateProvider>
// );

// // ✅ Register the main app component
// AppRegistry.registerComponent(appName, () => RootComponent);

// // ✅ Register background task
// const HeadlessTask = async (event) => {
//   console.log('[BackgroundFetch HeadlessTask] start:', event.taskId);
//   await refreshCustomers();
//   BackgroundFetch.finish(event.taskId);
// };

// BackgroundFetch.registerHeadlessTask(HeadlessTask);
