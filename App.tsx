import { ActivityIndicator, KeyboardAvoidingView, Platform, StatusBar, StyleSheet, Text, View } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createDrawerNavigator } from '@react-navigation/drawer';
import AsyncStorage from '@react-native-async-storage/async-storage';
// Screens
import QuantityPage from './screen/QuantityPage';
import CustomerScanCard from './screen/CustomerScanCard';
import NfcScan from './screen/NfcScan';
import TransactionData from './screen/TransactionData';
import CustomDrawerContent from './screen/CustomDrawerContent';
import { useStateContext } from './screen/StateProvider';
import { useEffect, useState } from 'react';
import CustomersList from './screen/CustomersList ';
import TodayTransactions from './screen/TodayTransactions';
import LoadingComponent from './screen/LoadingComponent';

// Initialize Stack and Drawer Navigators
const Stack = createStackNavigator();
const Drawer = createDrawerNavigator();

// ✅ Stack Navigator for Main Screens
const StackNavigation = () => {
  return (
    <Stack.Navigator>
      <Stack.Screen options={{ headerShown: false }} name="Home" component={QuantityPage} />
      <Stack.Screen options={{ headerShown: true,headerTintColor: '#fff', headerStyle:{backgroundColor:'#1677FF'} }} name="Transaction" component={TransactionData} />
      <Stack.Screen options={{ headerShown: true,headerTintColor: '#fff', headerStyle:{backgroundColor:'#1677FF'} }} name="TodayTransaction" component={TodayTransactions} />
      <Stack.Screen options={{ headerShown: false,headerTintColor: '#fff', headerStyle:{backgroundColor:'#1677FF'} }} name="CustomersList" component={CustomersList} />
      <Stack.Screen  options={({ navigation }) => ({
          headerShown: false,
          gestureEnabled: false,
          drawerLockMode: 'locked-closed', swipeEnabled: false,
           })} name="CustomerScanCard" component={CustomerScanCard} />
    </Stack.Navigator>
  );
};

// ✅ Drawer Navigation (Used when User is Logged In)
const DrawerNavigation = () => {
  return (
    <Drawer.Navigator drawerContent={(props) => <CustomDrawerContent {...props} />} screenOptions={{ drawerType: 'slide',  drawerStyle: { backgroundColor: 'white' }, }} >
      <Drawer.Screen options={{ headerShown: false }} name="HomeStack" component={StackNavigation} />
    </Drawer.Navigator>
  );
};

// ✅ Main App Component
const App = () => {
  const { auth, setAuth } = useStateContext();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkAuth = async () => {
      const isFirstLaunch = await AsyncStorage.getItem('isFirstLaunch');
      setAuth(!!isFirstLaunch); // Convert `isFirstLaunch` to boolean
      setLoading(false);
    };

    checkAuth();
  }, []);

  //  Show Loading Indicator Before Checking Auth State
  if (loading) {
    return (
      <LoadingComponent/>
    );
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <KeyboardAvoidingView style={{ flex: 1 }} behavior={Platform.OS === 'ios' ? 'padding' : undefined}>
        <NavigationContainer>
          <StatusBar backgroundColor="#1677FF" barStyle="light-content" />
          {auth ? (
            //  If User is Authenticated, Show Drawer Navigation
            <DrawerNavigation />
          ) : (
            //  If Not Logged In, Show NFC Scan Screen
            <Stack.Navigator>
              <Stack.Screen options={{ headerShown: false }} name="NfcScan" component={NfcScan} />
            </Stack.Navigator>
          )}
        </NavigationContainer>
      </KeyboardAvoidingView>
    </GestureHandlerRootView>
  );
};

//  Styles
const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f4f7',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#1677FF',
  },
});

export default App;
