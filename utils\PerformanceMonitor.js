import { Alert } from 'react-native';

class PerformanceMonitor {
  constructor() {
    this.memoryWarningThreshold = 100; // MB
    this.isMonitoring = false;
    this.monitoringInterval = null;
    this.memoryUsageHistory = [];
    this.maxHistoryLength = 10;
  }

  static getInstance() {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  // Start monitoring memory usage
  startMonitoring(intervalMs = 30000) { // Check every 30 seconds
    if (this.isMonitoring) {
      return;
    }

    this.isMonitoring = true;
    console.log('🔍 Performance monitoring started');

    this.monitoringInterval = setInterval(() => {
      this.checkMemoryUsage();
    }, intervalMs);
  }

  // Stop monitoring
  stopMonitoring() {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    this.isMonitoring = false;
    console.log('🛑 Performance monitoring stopped');
  }

  // Check current memory usage
  checkMemoryUsage() {
    try {
      // Note: React Native doesn't have direct memory access
      // This is a placeholder for memory monitoring logic
      // In production, you might use native modules or performance APIs
      
      const timestamp = new Date().toISOString();
      const memoryInfo = {
        timestamp,
        // Placeholder values - replace with actual memory monitoring
        heapUsed: Math.random() * 100,
        heapTotal: Math.random() * 200,
      };

      this.memoryUsageHistory.push(memoryInfo);
      
      // Keep only recent history
      if (this.memoryUsageHistory.length > this.maxHistoryLength) {
        this.memoryUsageHistory.shift();
      }

      // Check for memory warnings
      if (memoryInfo.heapUsed > this.memoryWarningThreshold) {
        this.handleMemoryWarning(memoryInfo);
      }

      console.log(`📊 Memory Usage: ${memoryInfo.heapUsed.toFixed(2)}MB`);
    } catch (error) {
      console.error('❌ Memory monitoring error:', error);
    }
  }

  // Handle memory warning
  handleMemoryWarning(memoryInfo) {
    console.warn(`⚠️ High memory usage detected: ${memoryInfo.heapUsed.toFixed(2)}MB`);
    
    // Trigger garbage collection suggestions
    this.suggestCleanup();
    
    // Optionally show user warning
    if (memoryInfo.heapUsed > this.memoryWarningThreshold * 1.5) {
      Alert.alert(
        'Memory Warning',
        'The app is using high memory. Consider restarting the app if it becomes slow.',
        [{ text: 'OK' }]
      );
    }
  }

  // Suggest cleanup actions
  suggestCleanup() {
    console.log('🧹 Suggesting memory cleanup...');
    
    // Force garbage collection if available
    if (global.gc) {
      global.gc();
      console.log('♻️ Garbage collection triggered');
    }
    
    // Clear image cache (if using image caching library)
    // ImageCache.clear();
    
    // Clear any temporary data
    this.clearTemporaryData();
  }

  // Clear temporary data
  clearTemporaryData() {
    // Clear memory usage history except recent entries
    if (this.memoryUsageHistory.length > 5) {
      this.memoryUsageHistory = this.memoryUsageHistory.slice(-5);
    }
  }

  // Get memory usage report
  getMemoryReport() {
    return {
      isMonitoring: this.isMonitoring,
      historyLength: this.memoryUsageHistory.length,
      recentUsage: this.memoryUsageHistory.slice(-3),
      averageUsage: this.calculateAverageUsage(),
    };
  }

  // Calculate average memory usage
  calculateAverageUsage() {
    if (this.memoryUsageHistory.length === 0) {
      return 0;
    }

    const total = this.memoryUsageHistory.reduce((sum, entry) => sum + entry.heapUsed, 0);
    return total / this.memoryUsageHistory.length;
  }

  // Performance optimization utilities
  static optimizeImages() {
    console.log('🖼️ Optimizing images...');
    // Implement image optimization logic
    // - Resize large images
    // - Compress images
    // - Use appropriate formats
  }

  static optimizeAnimations() {
    console.log('🎬 Optimizing animations...');
    // Implement animation optimization
    // - Use native driver when possible
    // - Reduce animation complexity
    // - Stop unnecessary animations
  }

  static optimizeDatabase() {
    console.log('🗄️ Optimizing database...');
    // Implement database optimization
    // - Clean old records
    // - Optimize queries
    // - Use batch operations
  }

  // Memory leak detection helpers
  static detectLeaks() {
    console.log('🔍 Detecting potential memory leaks...');
    
    const warnings = [];
    
    // Check for common leak patterns
    if (global.setInterval && global.setInterval.length > 5) {
      warnings.push('Multiple intervals detected - potential timer leaks');
    }
    
    if (global.setTimeout && global.setTimeout.length > 10) {
      warnings.push('Multiple timeouts detected - potential timer leaks');
    }
    
    return warnings;
  }

  // Cleanup utility for components
  static createCleanupHelper() {
    const timers = [];
    const listeners = [];
    
    return {
      addTimer: (timer) => timers.push(timer),
      addListener: (listener) => listeners.push(listener),
      cleanup: () => {
        timers.forEach(timer => clearTimeout(timer) || clearInterval(timer));
        listeners.forEach(listener => {
          if (listener && typeof listener.remove === 'function') {
            listener.remove();
          }
        });
        timers.length = 0;
        listeners.length = 0;
      }
    };
  }
}

export default PerformanceMonitor;
