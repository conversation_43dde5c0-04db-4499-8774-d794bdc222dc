// buildscript {
//     ext {
//         buildToolsVersion = "35.0.0"
//         minSdkVersion = 24
//         compileSdkVersion = 35
//         targetSdkVersion = 35
//         ndkVersion = "27.1.12297006"
//         kotlinVersion = "2.0.21"
//     }
//     repositories {
//         google()
//         mavenCentral()
//     }
//     dependencies {
//         classpath("com.android.tools.build:gradle")
//         classpath("com.facebook.react:react-native-gradle-plugin")
//         classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
//     }
// }

// apply plugin: "com.facebook.react.rootproject"


buildscript {
    ext {
        buildToolsVersion = "35.0.0"
        minSdkVersion = 24
        compileSdkVersion = 35
        targetSdkVersion = 35
        ndkVersion = "27.1.12297006"
        kotlinVersion = "2.0.21"
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        // Specify a version for com.android.tools.build:gradle (e.g., 8.8.0 as per your error log)
        classpath("com.android.tools.build:gradle:8.8.0")
        classpath("com.facebook.react:react-native-gradle-plugin")
        // Specify the version for kotlin-gradle-plugin explicitly
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:2.0.21")
    }
}

// Add the allprojects block to define repositories for dependency resolution
allprojects {
    repositories {
        mavenLocal() // Local Maven repo (optional but good practice)
        mavenCentral() // Central Maven repo
        google() // Google Maven repo (for Android dependencies)
        // React Native-specific repositories
        maven { url "$rootDir/../node_modules/react-native/android" }
        maven { url "$rootDir/../node_modules/jsc-android/dist" }
        // Add this for react-native-background-fetch to resolve tsbackgroundfetch
        maven { url "${project(':react-native-background-fetch').projectDir}/libs" }
        // Add Sunmi repository
        maven { url "https://jitpack.io" }
        maven { url "https://dl.bintray.com/sunmi/maven" }

    }
}

apply plugin: "com.facebook.react.rootproject"