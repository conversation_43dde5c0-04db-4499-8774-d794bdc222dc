{"name": "<PERSON>l", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@hendrysetiadi/react-native-sunmi-printer": "^0.2.0", "@react-native-async-storage/async-storage": "^2.1.1", "@react-native-community/masked-view": "^0.1.11", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/drawer": "^7.1.2", "@react-navigation/native": "^7.0.14", "@react-navigation/stack": "^7.1.1", "react": "19.0.0", "react-native": "^0.78.0", "react-native-background-fetch": "^4.2.7", "react-native-config": "^1.5.5", "react-native-device-info": "^14.0.4", "react-native-gesture-handler": "^2.24.0", "react-native-get-random-values": "^1.11.0", "react-native-nfc-manager": "^3.16.1", "react-native-reanimated": "^3.17.1", "react-native-reanimated-carousel": "^4.0.2", "react-native-safe-area-context": "^5.2.0", "react-native-screens": "^4.9.0", "react-native-sqlite-storage": "^6.0.1", "react-native-vector-icons": "^10.2.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.78.0", "@react-native/eslint-config": "0.78.0", "@react-native/metro-config": "0.78.0", "@react-native/typescript-config": "0.78.0", "@types/jest": "^29.5.13", "@types/react": "^19.0.0", "@types/react-test-renderer": "^19.0.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.0.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}