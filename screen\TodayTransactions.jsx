import { ActivityIndicator, <PERSON>ert, <PERSON><PERSON>resh<PERSON>ontrol, FlatList, StyleSheet, Text, TouchableOpacity, View, } from 'react-native';
import React, { useEffect, useState } from 'react';
import { openDatabase } from 'react-native-sqlite-storage';
import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';
import LoadingComponent from './LoadingComponent';
import Config from 'react-native-config';


const db = openDatabase({ name: 'UserDatabase.db' });

const TodayTransactions = () => {
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [todayLiter, setTodayLiter] = useState(0);

   const apiBaseUrl = Config.API_BASE_URL;
  

  const getTodayDate = () => {
    const today = new Date();
    return today.toLocaleDateString(); // Example: '4/10/2025'
  };

  const fetchTodayTransactions = async () => {
    try {
      setLoading(true);
      const todayDate = getTodayDate();

      await new Promise((resolve, reject) => {
        db.transaction((tx) => {
          tx.executeSql(
            'SELECT * FROM Transactions WHERE created_at LIKE ? ORDER BY id DESC',
            [`${todayDate}%`],
            (_, results) => {
              const temp = [];
              let totalQty = 0;

              for (let i = 0; i < results.rows.length; ++i) {
                const item = results.rows.item(i);
                temp.push(item);
                totalQty += parseFloat(item.quantity); // sum quantity
              }

              setTransactions(temp);
              setTodayLiter(totalQty); // ✅ update total liter
              resolve();
            },
            (_, error) => reject(error)
          );
        });
      });
    } catch (error) {
      console.error('Fetch error:', error);
      Alert.alert('Error', 'Failed to load today\'s transactions.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTodayTransactions();
  }, []);

  const syncAllTransactions = async () => {
    const netInfo = await NetInfo.fetch();
    if (!netInfo.isConnected) {
      console.log("⚠️ No internet connection. Skipping Transactions.");
      Alert.alert('⚠️ No internet connection.', ' plz connect your internet ');
      return;
    }
    const unsyncedTransactions = transactions.filter((item) => !item.sync);
    if (unsyncedTransactions.length === 0) {
      Alert.alert('No Unsynced Transactions', 'All transactions are already synced.');
      return;
    }

    try {
      setLoading(true);
      const B_TOKEN = await AsyncStorage.getItem('token');
      const token = JSON.parse(B_TOKEN);

      const response = await fetch(`${apiBaseUrl}/transactions/sync`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ transactions: unsyncedTransactions }),
      });

      const result = await response.json();
      console.log('Sync Response:', result);

      if (result.success) {
        // ✅ Mark them as synced in local DB
        db.transaction((tx) => {
          unsyncedTransactions.forEach((txn) => {
            tx.executeSql(
              'UPDATE Transactions SET sync = 1 WHERE id = ?',
              [txn.id],
              () => console.log(`Synced txn ${txn.id}`),
              (_, error) => console.log('SQLite update error:', error)
            );
          });
        });

        Alert.alert('Success', 'Transactions synced successfully!');
        await fetchTodayTransactions(); // Refresh list and total
      } else {
        Alert.alert('Sync Failed', 'Server did not accept the transactions.');
      }
    } catch (err) {
      console.error('Sync error:', err);
      Alert.alert('Error', 'Check your internet connection.');
    } finally {
      setLoading(false);
    }
  };


  const onRefresh = async () => {
    setRefreshing(true);
    await fetchTodayTransactions();
    setRefreshing(false);
  };

  if (loading && !refreshing) {
    return (
      <LoadingComponent/>
    );
  }

  return (
    <View style={{ flex: 1, backgroundColor: 'white' }}>
      <View style={[styles.row, { padding: 20 }]}>
        <Text style={{ fontWeight: 'bold', color: 'black' }}>Today's Transactions:</Text>
        <Text style={{ fontWeight: 'bold', color: 'black' }}> {transactions.length}</Text>
      </View>
      <View style={[styles.row, { padding: 20 }]}>
        <Text style={{ fontWeight: 'bold', color: 'black' }}>Today Total Liter's:</Text>
        <Text style={{ fontWeight: 'bold', color: 'black' }}> {todayLiter}</Text>
      </View>

      <FlatList
        data={transactions}
        keyExtractor={(item) => item.id.toString()}
        contentContainerStyle={{ paddingBottom: 60 }}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        renderItem={({ item }) => (
          <View
            style={[
              styles.transactionCard,
              { backgroundColor: item.sync ? 'rgba(0,128,0,0.2)' : 'rgba(255,0,0,0.2)' },
            ]}
          >
            <View style={{ flex: 1, padding: 5 }}>
              <View style={styles.row}>
                <Text style={styles.text}>Sr No: {item.serial_no}</Text>
                <Text style={styles.text}>Name: {item.name}</Text>
              </View>
              <View style={styles.row}>
                <Text style={styles.text}>Qty: {item.quantity}</Text>
                <Text style={styles.text}>Price: {item.total_price}</Text>
              </View>
              <View style={styles.row}>
                <Text style={styles.text}>Date: {item.created_at}</Text>
                <Text style={[styles.text, { color: item.sync ? 'green' : 'red' }]}>
                  {item.sync ? 'Synced' : 'Pending'}
                </Text>
              </View>
            </View>
          </View>
        )}
        ListEmptyComponent={
          <View style={styles.noTransactionCard}>
            <Text style={styles.noTransactionText}>No Transactions Found</Text>
          </View>
        }
      />

      <View style={styles.buttonContainer}>
        <TouchableOpacity
          disabled={transactions.length === 0 || loading}
          onPress={syncAllTransactions}
          style={[styles.button, { backgroundColor: transactions.length === 0 || loading ? '#8EC3E6' : '#1677ff', },]}>
          <Text style={styles.buttonText}>{loading ? 'Syncing...' : 'Sync All'}</Text>
        </TouchableOpacity>
      
      </View>

    </View>
  );
};

export default TodayTransactions;

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f4f7',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#1677FF',
  },
  noTransactionCard: {
    width: '90%',
    height: 200,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
    elevation: 3,
    backgroundColor: '#fff',
    marginHorizontal: 'auto',
    marginTop: 20,
  },
  noTransactionText: {
    fontSize: 16,
    color: 'gray',
  },
  transactionCard: {
    width: '90%',
    padding: 1,
    borderRadius: 10,
    elevation: 3,
    marginHorizontal: 'auto',
    marginTop: 10,
    backgroundColor: '#fff',
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 2,
  },
  text: {
    fontSize: 12,
    color: 'black',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 10,
  },
  button: {
    borderRadius: 10,
    padding: 10,
    width: '45%',
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
  },
});
